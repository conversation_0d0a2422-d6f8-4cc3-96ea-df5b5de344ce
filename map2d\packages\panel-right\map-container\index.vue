<template>
  <div>
    <!-- 调试信息 -->
    <div style="background: yellow; padding: 10px; margin: 5px;">
      调试: containerType = {{ containerType }}
    </div>

    <!-- 标题 -->
    <tab-title title="lang.rms.fed.container" />
    <!-- 搜索框 -->
    <search-box ref="searchBox" />

    <panel-shelf v-if="containerType == 'shelf'" class="container-item" />

    <panel-rack
      v-if="containerType == 'rack' || mapConfig?.allRackContainer"
      @rectEnd="rectEnd"
      class="container-item"
    />
    <!-- 添加托盘支架组件 -->
    <panel-pallet-rack v-if="containerType == 'pallet'" @rectEnd="rectEnd" class="container-item" />
    <!-- 添加ppp货架组件 -->
    <panel-poppick v-if="containerType == 'poppick'" class="container-item" />

    <panel-x-shelf v-if="containerType == 'xShelf'" class="container-item" />
  </div>
</template>

<script>
import { mapState, mapActions } from "pinia";
import { useMap2dStore } from "@map2d/dataStore/index";
import { useMap2dContainerStore } from "@map2d/dataStore/right-panel/container";
import TabTitle from "@map2d/packages/common/tab-title.vue";
import SearchBox from "./search-box.vue";
import PanelShelf from "./shelf/index.vue";
import PanelRack from "./rack/index.vue";
import PanelPoppick from "./poppick/index.vue";
import PanelXShelf from "./xShelf/index.vue";
// 导入托盘支架组件
import PanelPalletRack from "./palletRack/index.vue";

export default {
  name: "map-container",
  components: {
    TabTitle,
    SearchBox,
    PanelShelf,
    PanelRack,
    PanelPoppick,
    PanelXShelf,
    PanelPalletRack,
  },
  computed: {
    ...mapState(useMap2dStore, ["isRightOccupy", "mapClickData", "mapConfig"]),
    ...mapState(useMap2dContainerStore, ["containerType"]),
  },
  watch: {
    mapClickData: {
      handler(data) {
        console.log("=== mapClickData 调试 ===");
        console.log("接收到的数据:", data);
        console.log("isRightOccupy:", this.isRightOccupy);
        console.log("data?.layer:", data?.layer);
        console.log("data?.options?.shelfType:", data?.options?.shelfType);
        // 如果右侧面板被占用，则不处理点击事件
        if (this.isRightOccupy) return;
        // 只处理这5种类型的地图元素点击
        if (!["shelf", "rack", "poppick", "xShelf", "pallet"].includes(data?.layer)) return;
        // 清空搜索数据
        this.setSearchData(null);
        const shelfType = data?.options?.shelfType;
        switch (shelfType) {
          case "PPP_SHELF":
            // 这个方法就是来设置 containerType 的值的
            this.setContainerType("poppick");
            break;
          case "X_HOLDER":
          case "X_HOLDER_PALLET":
          case "X_HOLDER_PALLET_STACK":
          case "X_PALLET_STACK":
          case "X_PALLET":
            this.setContainerType("xShelf");
            break;
          case "PALLET_RACK":
            console.log("匹配到 PALLET_RACK，设置 containerType 为 pallet");
            console.log("调用 setContainerType 前的 containerType:", this.containerType);
            this.setContainerType("pallet");
            console.log("调用 setContainerType 后的 containerType:", this.containerType);
            break;
          default:
            this.setContainerType(data?.layer);
            break;
        }
      },
      immediate: true,
    },

    containerType: {
      handler(newVal, oldVal) {
        console.log("=== containerType 变化 ===");
        console.log("旧值:", oldVal);
        console.log("新值:", newVal);
        console.log("是否为 pallet:", newVal === 'pallet');
      },
      immediate: true,
    },
  },
  created() {},
  beforeUnmount() {
    this.resetContainerStoreData();
  },

  methods: {
    ...mapActions(useMap2dContainerStore, [
      "setContainerType",
      "resetContainerStoreData",
      "setSearchData",
    ]),

    rectEnd() {
      this.$refs.searchBox.rectEnd();
    },
  },
};
</script>

<style lang="less" scoped>
.container-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}
</style>
