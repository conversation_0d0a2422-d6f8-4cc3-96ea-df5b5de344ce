<template>
  <div v-if="palletRackData">
    <h3 class="rack-title">
      <span class="title-code">
        {{
          palletRackData?.modelCategory === "PALLET"
            ? $t("lang.rms.fed.currentPallet")
            : $t("lang.rms.fed.currentPalletRack")
        }}
        :<strong>{{ palletRackData?.shelfCode || "" }}</strong>
      </span>
    </h3>
    <div class="map2d-pallet-rack-list">
      <div
        v-for="(items, i) in lattices"
        :key="i"
        :class="`rack-list-item ${items.length === 1 ? 'single-item' : ''}`"
      >
        <span
          v-for="(item, j) in items"
          :key="j"
          :class="
            [
              item.hasOwnProperty('occupiedContainer') ? 'has-pallet' : '',
              item?.lockedState === 'LOCKED' || item?.occupiedContainer?.lockedState === 'LOCKED'
                ? 'locked'
                : '',
              item.palletLatticeCode === latticeBox || latticeCodes.includes(item.palletLatticeCode)
                ? 'active'
                : '',
            ].join(' ')
          "
          :title="item.palletLatticeCode || ''"
          @click="currentSelect(item)"
        >
          <i class="icon-status">
            <el-icon
              v-if="
                item?.lockedState == 'LOCKED' || item?.occupiedContainer?.lockedState === 'LOCKED'
              "
            >
              <gp-icon-lock />
            </el-icon>
          </i>
          <i v-if="item.occupiedContainer?.shelfCode" class="icon-pallet" />
          {{
            item.occupiedContainer?.shelfCode
              ? item.occupiedContainer?.shelfCode
              : item.palletLatticeCode
          }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "pinia";
import { useMap2dContainerStore } from "@map2d/dataStore/right-panel/container";

export default {
  name: "CurrentPalletRack",
  props: ["palletRackData"],
  data() {
    return {
      lattices: [],
      latticeBox: "",
      latticeCodes: [],
    };
  },
  watch: {
    palletRackData: {
      handler(data) {
        this.setRackCurrentSelected(null);
        this.latticeBox = "";
        this.latticeCodes = [];

        if (!data) {
          this.lattices = [];
          return;
        }

        const rackLattices = data?.lattices || [];
        const latticesLen = rackLattices.length;
        if (!rackLattices.length || latticesLen <= 0) {
          this.lattices = [];
          return;
        }

        // 按层级组织托盘位
        const cacheLattices = rackLattices.filter(item => item.layer === 0);
        const otherLattices = rackLattices.filter(item => item.layer !== 0);
        const cacheLen = cacheLattices.length;
        const otherLen = otherLattices.length;

        let layers = data?.layerNum || Math.round(otherLen);
        let arr = [];
        if (cacheLen > 0) --layers;

        let items;
        for (let i = layers; i > 0; i--) {
          items = otherLattices.filter(item => item.layer === i);
          if (items.length) arr.push(items);
          else arr.push([false]);
        }

        // 缓存位最后处理
        if (cacheLen > 0) {
          arr.push(cacheLattices);
        }
        this.lattices = arr;
      },
      immediate: true,
    },
  },
  methods: {
    ...mapActions(useMap2dContainerStore, ["setRackCurrentSelected"]),

    currentSelect(lattice) {
      const palletLatticeCode = lattice.palletLatticeCode;

      if (lattice.hasOwnProperty("occupiedContainer")) {
        const code = lattice.occupiedContainer?.shelfCode || "";
        this.latticeBox = palletLatticeCode;
        this.latticeCodes = [];
        this.setRackCurrentSelected({
          type: "pallet",
          data: code,
          lattice,
          extra: palletLatticeCode,
        });
        return;
      }

      this.latticeBox = "";
      let codes;
      if (this.latticeCodes.includes(palletLatticeCode)) {
        codes = this.latticeCodes.filter(code => code !== palletLatticeCode);
      } else {
        codes = this.latticeCodes.concat(palletLatticeCode);
      }
      this.latticeCodes = codes;
      this.setRackCurrentSelected({
        type: "lattice",
        data: codes,
        lattices: codes.map(code => ({ palletLatticeCode: code })),
      });
    },
  },
};
</script>

<style lang="less" scoped>
.rack-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: normal;

  .title-code {
    color: #333;
    strong {
      color: #1890ff;
      margin-left: 4px;
    }
  }
}

.map2d-pallet-rack-list {
  .rack-list-item {
    display: flex;
    margin-bottom: 4px;

    &.single-item {
      justify-content: center;
    }

    span {
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 60px;
      height: 32px;
      margin-right: 4px;
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background: #e6f7ff;
        border-color: #40a9ff;
      }

      &.has-pallet {
        background: #f6ffed;
        border-color: #52c41a;
        color: #52c41a;
      }

      &.locked {
        background: #fff1f0;
        border-color: #ff4d4f;
        color: #ff4d4f;
      }

      &.active {
        background: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }

      .icon-status {
        position: absolute;
        top: 2px;
        right: 2px;
        font-size: 10px;
      }

      .icon-pallet {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 8px;
        height: 8px;
        background: #52c41a;
        border-radius: 50%;
      }
    }
  }
}
</style>
