<template>
  <detail-box>
    <el-tabs
      v-model="activeName"
      class="content-tabs"
      :class="{ single: !lattices && !pallet }"
      @tab-click="tabClick"
    >
      <el-tab-pane :label="$t('lang.rms.fed.palletRack')" name="rack">
        <detail-box-grids :data="palletRackDetail" />
      </el-tab-pane>

      <!-- lattice -->
      <el-tab-pane
        v-if="lattices && lattices?.length > 0"
        :label="$t('lang.rms.fed.palletLattice')"
        name="lattice"
      >
        <div
          v-for="item in lattices"
          :key="item.palletLatticeCode"
          style="border-bottom: 6px solid #ddd"
        >
          <detail-box-grids :data="latticeDetail(item)">
            <template #exImg="{ item }">
              <el-link
                type="danger"
                :disabled="loadingViewExImg"
                @click="errorHandleFn(item.value)"
              >
                {{ $t("lang.rms.fed.buttonView") }}
                <el-icon v-if="loadingViewExImg && codeForViewExImg == item.value">
                  <gp-icon-loading />
                </el-icon>
              </el-link>
            </template>
          </detail-box-grids>
        </div>
      </el-tab-pane>

      <!-- pallet -->
      <el-tab-pane v-if="pallet" :label="$t('lang.rms.fed.pallet')" name="pallet">
        <detail-box-grids :data="palletDetail" />
      </el-tab-pane>
    </el-tabs>
  </detail-box>
</template>

<script>
import { mapState } from "pinia";
import { useMap2dContainerStore } from "@map2d/dataStore/right-panel/container";
import DetailBox from "@map2d/packages/common/detail-box.vue";
import DetailBoxGrids from "@map2d/packages/common/detailBoxGrids/index.vue";

export default {
  name: "PalletRackDetail",
  components: { DetailBox, DetailBoxGrids },
  props: ["data", "loadingViewExImg"],
  data() {
    return {
      activeName: "rack",
      manualClickTabName: null,
      lattices: null,
      pallet: null,
      codeForViewExImg: null,
    };
  },
  computed: {
    ...mapState(useMap2dContainerStore, ["rackCurrentSelected"]),

    palletRackDetail() {
      if (!this.data) return [];

      const data = this.data || {};
      const detail = [
        { label: "lang.rms.fed.palletRackCode", value: data.shelfCode, validate: "empty" },
        { label: "lang.rms.fed.palletRackLocation", value: data.location, validate: "position" },
        {
          label: "lang.rms.fed.palletRackLocationCode",
          value: data.locationCell,
          validate: "empty",
        },
        { label: "lang.rms.fed.logicId", value: data.areaId, validate: "empty" },
        { label: "lang.rms.fed.palletRackLayers", value: data.layerNum, validate: "empty" },
        {
          label: "lang.rms.fed.palletNumber",
          value: data.hasOwnProperty("containerNum") ? data.containerNum : "--",
          validate: "empty",
        },
      ];

      return detail;
    },

    latticeDetail() {
      return item => {
        if (!item) return [];
        const data = item || {};
        let arr = [
          {
            label: "lang.rms.fed.palletLatticeCode",
            value: data.palletLatticeCode,
            validate: "empty",
          },
          {
            label: "lang.rms.fed.palletLatticeOccupiedStatus",
            value: data.occupiedState,
            validate: "empty",
          },
          {
            label: "lang.rms.fed.palletLatticeLockedStatus",
            value: data.lockedState,
            validate: "empty",
          },
          { label: "lang.rms.fed.palletLatticeLayer", value: data.layer, validate: "empty" },
          {
            label: "lang.rms.fed.palletLatticeHight",
            value: data.height ? data.height + "mm" : "--",
            validate: "empty",
          },
          { label: "lang.rms.fed.fetchDirs", value: data.fetchDirs, validate: "empty" },
        ];

        if (data.exImg) {
          arr.push({
            label: "lang.rms.fed.exceptionHandling",
            value: data.palletLatticeCode,
            slotName: "exImg",
          });
        }

        return arr;
      };
    },

    palletDetail() {
      if (!this.pallet) return [];

      const data = this.pallet || {};
      const detail = [
        {
          label: "lang.rms.palletSearchManage.palletCode",
          value: data.shelfCode,
          validate: "empty",
        },
        {
          label: "lang.rms.palletSearchManage.palletStatus",
          value: data.workState,
          validate: "empty",
        },
        { label: "lang.rms.fed.palletLockedStatus", value: data.lockedState, validate: "empty" },
        { label: "lang.rms.fed.palletLocation", value: data.location, validate: "position" },
        { label: "lang.rms.fed.robotId", value: data.robotId, validate: "empty" },
        { label: "lang.rms.fed.cellCode", value: data.locationCell, validate: "empty" },
      ];
      return detail;
    },
  },
  watch: {
    data: {
      handler(newData, oldData) {
        if (!newData || !oldData || newData?.shelfCode !== oldData?.shelfCode) {
          this.manualClickTabName = null;
        }
        this.resolveDetail();
      },
      immediate: true,
    },
    rackCurrentSelected: {
      handler(newSelData, oldSelData) {
        if (
          !newSelData ||
          !oldSelData ||
          newSelData?.type !== oldSelData?.type ||
          (newSelData?.type === "pallet" &&
            oldSelData?.type === "pallet" &&
            newSelData?.lattice?.occupiedContainer?.shelfCode !==
              oldSelData?.lattice?.occupiedContainer?.shelfCode) ||
          (newSelData?.type === "lattice" && oldSelData?.type === "lattice")
        ) {
          this.manualClickTabName = null;
        }

        this.resolveDetail();
      },
      immediate: true,
    },
  },
  methods: {
    resolveDetail() {
      const nLattices = this.data?.lattices || [];
      const selected = this.rackCurrentSelected;
      const selectedType = selected?.type;

      switch (selectedType) {
        case "lattice":
          this.activeName = this.manualClickTabName || "lattice";
          this.lattices = selected.lattices.map(item => {
            const lattice = nLattices.find(l => l.palletLatticeCode === item.palletLatticeCode);
            return lattice;
          });

          if (this.lattices.length === 0) {
            this.activeName = "rack";
          }
          this.pallet = null;
          break;
        case "pallet":
          const lattice = nLattices.find(
            item => selected.lattice.palletLatticeCode === item.palletLatticeCode,
          );
          this.lattices = [lattice];

          if (lattice?.occupiedContainer?.shelfCode) {
            this.activeName = this.manualClickTabName || "pallet";
            this.pallet = lattice?.occupiedContainer;
          } else {
            this.activeName = this.manualClickTabName || "lattice";
            this.pallet = null;
          }
          break;
        default:
          this.activeName = "rack";
          this.lattices = null;
          this.pallet = null;
          break;
      }
    },

    tabClick(tab) {
      this.manualClickTabName = tab?.props?.name;
    },

    errorHandleFn(latticeCode) {
      this.codeForViewExImg = latticeCode;
      this.$emit("errorHandle", { type: "lattice", code: latticeCode });
    },
  },
};
</script>
