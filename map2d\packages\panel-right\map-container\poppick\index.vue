<template>
  <div>
    <div v-show="!operation" class="component-btn-group">
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfUpdate')"
        size="small"
        type="primary"
        @click="controlHandler('update')"
      >
        {{ $t("lang.rms.fed.updateShelf") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfMove')"
        size="small"
        type="primary"
        @click="controlHandler('move')"
      >
        {{ $t("lang.rms.fed.moveTypeShelf") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfUpdateAngel')"
        size="small"
        type="primary"
        @click="controlHandler('updateAngel')"
      >
        {{ $t("lang.rms.fed.updateShelfAngle") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfLock')"
        v-show="shelfData?.lockedState != 'LOCKED'"
        size="small"
        type="primary"
        @click="controlHandler('lock')"
      >
        {{ $t("lang.rms.fed.lockShelf") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfLock')"
        v-show="shelfData?.lockedState == 'LOCKED'"
        size="small"
        type="primary"
        class="dark"
        @click="controlHandler('unlock')"
      >
        {{ $t("lang.rms.fed.unlockShelf") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.latticeLock')"
        v-show="btnShow.lockLattice"
        size="small"
        type="primary"
        @click="controlHandler('lockLattice')"
      >
        {{ $t("lang.rms.fed.lockLattice") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.latticeLock')"
        v-show="btnShow.unlockLattice"
        size="small"
        type="primary"
        class="dark"
        @click="controlHandler('unlockLattice')"
      >
        {{ $t("lang.rms.fed.UnlockLattice") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.boxLock')"
        v-show="btnShow.lockBox"
        size="small"
        type="primary"
        @click="controlHandler('lockBox')"
      >
        {{ $t("lang.rms.fed.lockBox") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.boxLock')"
        v-show="btnShow.unlockBox"
        size="small"
        type="primary"
        class="dark"
        @click="controlHandler('unlockBox')"
      >
        {{ $t("lang.rms.fed.unlockBox") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.boxUpdate')"
        v-show="btnShow.updateBox"
        size="small"
        type="primary"
        @click="controlHandler('updateBox')"
      >
        {{ $t("lang.rms.fed.updateBox") }}
      </el-button>

      <el-button
        v-show="showReturnShelfBtn"
        size="middle"
        type="primary"
        @click="controlHandler('returnShelf')"
      >
        {{ $t("lang.rms.fed.ppinspect.returnShelf") }}
      </el-button>
    </div>
    <!-- 显示左上角货架信息的块 -->
    <drawer-panel ref="drawer" title="lang.rms.fed.container" class="ppp-drawer">
      <current-ppp :shelfData="shelfData" />

      <target-ppp
        v-if="targetShelfCode"
        :shelfCode="targetShelfCode"
        :latticeCode="targetLatticeCode"
      />
    </drawer-panel>
    <!-- 更新货架的表单 -->
    <ppp-update
      v-if="operation === 'update'"
      :cellCode="cellCode"
      :shelfAngel="shelfData?.angle"
      @updateCellCode="val => (cellCode = val)"
      @cancel="operation = undefined"
    />

    <ppp-move
      v-if="operation === 'move'"
      :cellCode="cellCode"
      :cellType="cellType"
      @updateCellCode="val => (cellCode = val)"
      @cancel="operation = undefined"
    />

    <ppp-update-angle
      v-if="operation === 'updateAngel'"
      :shelfAngel="shelfData?.angle"
      @cancel="operation = undefined"
    />

    <ppp-box-update
      v-if="operation === 'updateBox'"
      :targetShelfCode="targetShelfCode"
      @updateTargetShelfCode="val => (targetShelfCode = val)"
      @updateTargetLatticeCode="val => (targetLatticeCode = val)"
      @cancel="operation = undefined"
    />
    <!-- 所有的详情数据 -->

    <detail v-if="shelfData" :data="shelfData" />
  </div>
</template>

<script>
import isEqual from "lodash/isEqual";
import { mapState, mapActions } from "pinia";
import { useMap2dStore } from "@map2d/dataStore/index";
import { useMap2dContainerStore } from "@map2d/dataStore/right-panel/container";
import { useRootMenuStore } from "@stores/rootMenuStore";
import { getMap2D } from "@map2d/singleton";

import DrawerPanel from "@map2d/packages/common/drawer-panel.vue";
import CurrentPpp from "./ppp-current.vue";
import TargetPpp from "./ppp-target.vue";
import PppUpdate from "./ppp-update.vue";
import PppMove from "./ppp-move.vue";
import PppUpdateAngle from "./ppp-update-angle.vue";
import PppBoxUpdate from "./ppp-box-update.vue";
import Detail from "./ppp-detail.vue";

export default {
  name: "map-container-ppp",
  components: {
    DrawerPanel,
    CurrentPpp,
    TargetPpp,
    PppUpdate,
    PppMove,
    PppUpdateAngle,
    PppBoxUpdate,
    Detail,
  },
  data() {
    return {
      shelfData: null, // 当前选中的货架数据
      targetShelfCode: "", // 更新操作的 输入货架编号
      targetLatticeCode: "", // 更新操作的 输入的货位编号
      cellCode: "",
      cellType: "",
      operation: undefined, // move | update | updateAngle | updateBox
      mapClickFlag: false, // 地图点击标识

      showReturnShelfBtn: false, // 是否显示返回货架按钮
    };
  },
  computed: {
    ...mapState(useMap2dStore, ["mapClickData", "wsQueryTimer", "isRightDrawerVisible"]),
    ...mapState(useMap2dContainerStore, ["containerCode", "pppCurrentSelected"]),
    ...mapState(useRootMenuStore, ["getBtnPermission"]),

    btnShow() {
      const selected = this.pppCurrentSelected;
      let data = {
        lockLattice: false, // 锁定货位
        unlockLattice: false, // 解锁货位
        lockBox: false, // 锁定货箱
        unlockBox: false, // 解锁货箱
        updateBox: false,
      };

      const selectedType = selected?.type || "";
      switch (selectedType) {
        case "lattice":
          if (selected.hasOwnProperty("___latticeLock")) {
            data.lockLattice = !selected.___latticeLock;
            data.unlockLattice = !data.lockLattice;
          } else {
            const lattices = selected.lattices || [];
            const isUnlockedIndex = lattices.findIndex(item => item?.latticeFlag != "LOCKED");
            const isLockedIndex = lattices.findIndex(item => item?.latticeFlag == "LOCKED");
            data.lockLattice = isUnlockedIndex != -1;
            data.unlockLattice = isLockedIndex != -1;
          }
          data.lockBox = false;
          data.unlockBox = false;
          data.updateBox = false;
          break;
        case "box":
          const lattice = selected.lattice;
          if (selected.hasOwnProperty("___latticeLock")) {
            data.lockLattice = !selected.___latticeLock;
          } else {
            data.lockLattice = lattice?.latticeFlag != "LOCKED";
          }
          data.unlockLattice = !data.lockLattice;

          if (selected.hasOwnProperty("___boxLock")) data.lockBox = !selected.___boxLock;
          else data.lockBox = lattice?.relateBox?.lockState != 1;
          data.unlockBox = !data.lockBox;
          data.updateBox = true;
          break;
        default:
          break;
      }

      return data;
    },
  },
  watch: {
    mapClickData: {
      handler(data, oldData) {
        console.log("mapClickData", data);
        console.log("oldData", oldData);
        // 如果新值和旧值都不存在，则不处理
        if (!oldData && !data) return;
        // 从点击的数据中提取图层类型
        const layer = data?.layer;
        // 只处理这3种类型的地图元素点击  如果不是这3种类型，则清空数据
        if (!["shelf", "poppick", "cell"].includes(layer)) {
          this.setSearchData(null);
          this.clearData();
          return;
        }
        // 从点击的数据中提取元素编码
        const code = data.code.toString();
        switch (layer) {
          case "shelf":
          case "poppick":
            // 这是一次地图点击操作  后续用于判断是否需要自动打开抽屉面板
            this.mapClickFlag = true;
            // 如果当前处于"更新货箱"操作模式
            if (["updateBox"].includes(this.operation)) {
              console.log("更新货箱");
              // 更新 货箱
              // 将点击的货架编码赋值给 目标货架编码
              this.targetShelfCode = code;
              const $drawer = this.$refs["drawer"];
              $drawer && $drawer.open();
            } else if (this.containerCode !== code) {
              console.log("查询数据=========code ===", code);
              this.setContainerCode(code);
            }
            break;
          case "cell":
            if (code !== this.cellCode) {
              this.cellCode = code;
              this.cellType = data?.options?.cellType || ""; // 单元格类型
            }
            break;
        }
      },
      immediate: true,
    },
    // 监听containerCode 的变化 这里的containerCode 就是货架的编码 也就是mapClickData 中的 code
    containerCode: {
      handler(code) {
        this.clearData();

        if (code) {
          console.log("监听containerCode的值 ===", code);
          this.mapClickFlag = true;
          // 这个是调用的查询方法
          this.queryShelfData();
        }
      },
      immediate: true,
    },

    operation(val, oldVal) {
      this.targetShelfCode = null;
      this.targetLatticeCode = null;
      this.cellCode = "";
      this.cellType = "";
      this.setPppTargetSelected(null);
      this.setRightOccupyState(!!val);

      switch (oldVal) {
        case "update":
        case "move":
        case "updateBox":
        case "updateAngel":
          if (oldVal == "updateBox") this.setPppCurrentSelected(null);
          const code = this.containerCode;
          const { mapRender } = getMap2D();
          if (code) mapRender.select({ shelf: [code] });
          else mapRender.clearSelects();
          break;
      }
    },
  },

  beforeUnmount() {
    this.setRightOccupyState(false);
    this.clearData();
    Object.assign(this.$data, this.$options.data());
  },

  methods: {
    ...mapActions(useMap2dStore, ["setRightOccupyState", "setWsQueryTimer"]),
    ...mapActions(useMap2dContainerStore, [
      "setContainerCode",
      "setSearchData",
      "setPppCurrentSelected",
      "setPppTargetSelected",
    ]),

    /**
     * 关闭抽屉 -- 数据切换的时候 会出现 抽屉 再开启的 闪烁情况，因此把 closeDrawer 方法 抽出来
     * PS 判断 弹层真的 开着 再执行关闭
     */
    closeDrawer() {
      // 如果抽屉不可见 直接返回
      if (!this.isRightDrawerVisible) return;
      const $drawer = this.$refs["drawer"];
      $drawer && $drawer.close();
    },
    clearData() {
      this.setWsQueryTimer(null);
      this.shelfData = null;
      this.setPppCurrentSelected(null);

      this.targetShelfCode = "";
      this.targetLatticeCode = "";
      this.cellCode = "";
      this.cellType = "";
      this.setPppTargetSelected(null);

      const { mapRender } = getMap2D();
      mapRender?.renderBiz("placement", null, false);
    },
    queryShelfData() {
      // 清除之前设置的webSocket 定时器
      this.setWsQueryTimer(null);
      // 当前选中的货架编码
      const code = this.containerCode;
      // mapWorker 通信工作器用于与后端通信
      // mapRender 地图渲染用于在地图上展示效果
      const { mapWorker, mapRender } = getMap2D();
      // 发起webSocket 请求 查询货架数据
      mapWorker.wsDataQuery("SHELF", code).then(res => {
        // 如果请求失败 则提示错误信息 并返回
        if (res?.header?.code !== 0) {
          this.$error(this.$t(res?.header?.msg));
          return;
        }

        const data = res?.body;
        this.showReturnShelfBtn = data?.showReturnShelfBtn;
        // 如果没有数据 则清空数据 关闭抽屉面板
        if (!data) {
          this.clearData();
          this.closeDrawer();
          this.setSearchData(null);
          this.setContainerCode("");
          return;
        }

        data.shelfCode = code;
        // 如果当前处于地图点击操作 则打开抽屉面板
        if (this.mapClickFlag) {
          // 获取抽屉面板的vuey引用
          const $drawer = this.$refs["drawer"];
          if ($drawer) {
            // 调用抽屉的open方法显示详情
            $drawer.open();
            // 将mapClickFlag 设置为false  表示已经处理过地图点击操作了
            this.mapClickFlag = false;
          }
        }
        // mapRender?.renderBiz("placement", data, false);
        if (!isEqual(data, this.shelfData)) this.shelfData = data;

        const _timer = setTimeout(() => {
          this.queryShelfData();
        }, 300);
        this.setWsQueryTimer(_timer);
      });
    },

    controlHandler(cmd) {
      switch (cmd) {
        case "lock": // 锁定货箱架
          this._operateShelf("LOCK_SHELF");
          return;
        case "unlock": // 解锁货箱架
          this._operateShelf("UNLOCK_SHELF");
          return;
        case "returnShelf":
          this._operateShelf("RETURN_PP_INPSECT_EXC_SHELF");
          return;
        case "lockLattice": // 锁定货位
          this._operateLattice("LOCK_LATTICE");
          return;
        case "unlockLattice": // 解锁货位
          this._operateLattice("UNLOCK_LATTICE");
          return;
        case "lockBox": // 锁定货箱
          this._operateBox("LOCK_BOX");
          return;
        case "unlockBox": // 锁定货箱
          this._operateBox("UNLOCK_BOX");
          return;
        case "move": // 移动
        case "update": // 更新
        case "updateAngel": // 更新角度
          this.operation = cmd;
          return;
        case "updateBox": // 更新货箱
          this.operation = cmd;
          return;
      }
    },

    _operateBox(cmd) {
      this.operation = undefined;
      const selected = this.pppCurrentSelected;
      if (selected?.type !== "box") return;

      const map2D = getMap2D();
      map2D.mapWorker
        .wsRequest("WarehouseInstructionRequestMsg", {
          instruction: cmd,
          boxCode: selected.lattice.relateBoxCode,
        })
        .then(res => {
          const msg = $utils.Tools.transMsgLang(res?.header?.msg || "");
          if (res?.header?.code === 0) {
            this.$success(msg);
            if (cmd == "LOCK_BOX") selected.___boxLock = true;
            else selected.___boxLock = false;
          } else this.$error(msg);
        });
    },

    _operateLattice(cmd) {
      this.operation = undefined;
      const selected = this.pppCurrentSelected;
      const selectedType = selected?.type || "";
      if (!["lattice", "box"].includes(selectedType)) return;
      let latticeCodeList = [];
      if (selectedType == "lattice") {
        latticeCodeList = selected.lattices.map(item => item.latticeCode);
      } else {
        latticeCodeList = [selected.lattice.latticeCode];
      }

      const map2D = getMap2D();
      map2D.mapWorker
        .wsRequest("BoxInstructionRequestMsg", {
          instruction: cmd,
          latticeCodeList,
        })
        .then(res => {
          const msg = $utils.Tools.transMsgLang(res?.header?.msg || "");
          if (res?.header?.code === 0) {
            if (cmd == "LOCK_LATTICE") selected.___latticeLock = true;
            else selected.___latticeLock = false;
            this.$success(msg);
          } else this.$error(msg);
        });
    },

    // 货架操作
    _operateShelf(cmd) {
      this.operation = undefined;
      const shelfCode = this.containerCode;
      if (!shelfCode) {
        const msg = this.$t("lang.rms.fed.pleaseEnterTheShelfID"); // 机器人编号不能为空
        this.$error(msg);
        return;
      }
      const map2D = getMap2D();
      map2D.mapWorker
        .wsRequest("ShelfInstructionRequestMsg", {
          instruction: cmd,
          shelfCode,
        })
        .then(res => {
          const msg = $utils.Tools.transMsgLang(res?.header?.msg || "");
          if (res?.header?.code === 0) this.$success(msg);
          else this.$error(msg);
        });
    },
  },
};
</script>
<style lang="less" scoped>
.ppp-drawer {
  width: 350px;
}
</style>
