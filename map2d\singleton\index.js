/* ! <AUTHOR> at 2023/07/18 */
import { useRootStore } from "@stores/rootStore"; // 引入root数据管理
import { useMap2dStore } from "@map2d/dataStore/index";
import { useMap2dAreaStore } from "@map2d/dataStore/areas";
import { useMap2dAbnormalStore } from "@map2d/dataStore/top-panel/panel-abnormal";
import { useMap2dInfoStore } from "@map2d/dataStore/map-info";
import { useMap2dFastSearchStore } from "@map2d/dataStore/fast-search";
import { useMap2dTopPanelStore } from "@map2d/dataStore/top-panel/top-panel";

class MapSingleton {
  static _instance = null;

  static async createInstance($dom) {
    if (!$dom) {
      const { warn } = console;
      warn("【map2d create】::没有传入相关地图dom~");
      return;
    }
    const mapSingleton = new MapSingleton($dom);
    MapSingleton._instance = mapSingleton;
    return mapSingleton;
  }
  static getInstance() {
    if (!MapSingleton._instance) {
      console.warn("【map2d get】地图实例不存在，请刷新页面");
    } else {
      return MapSingleton._instance;
    }
  }

  constructor($dom) {
    if (!$dom) throw new Error("没有dom");

    // 组件 mounted 时 map 还未 ready，所以在这里初始化
    const map2dInfoStore = useMap2dInfoStore(); // 地图信息数据Store
    map2dInfoStore.setMapSetting({
      mapAngle: this._getMapAngle(),
      classicIcon: $utils.Data.getMap2dToolStatus("classicIcon") || false,
    });

    // 地图相关配置
    const options = {
      color: map2dInfoStore.mapColor,
      setting: map2dInfoStore.mapSetting,
    };

    this.$mapDom = $dom;
    this.mapRender = null;
    this.mapWorker = null;

    // 创建地图渲染实例
    if (__RMS_IS_DEV__ && import.meta.env.VITE_DEV_MAP2D == "true") {
      import("./mapDev.js").then(({ initMap2d }) => {
        this.init(initMap2d, $dom, options);
      });
    } else {
      import("./mapProd.js").then(({ initMap2d }) => {
        this.init(initMap2d, $dom, options);
      });
    }
  }

  init(initMap2d, $dom, options) {
    const binaryClient = useMap2dStore().isWsBinaryClient;
    let { mapRender, mapWorker } = initMap2d($dom, options, binaryClient);

    this._bindRenderEvent(mapRender);
    this.mapRender = mapRender;

    this._bindWorkerEvent(mapWorker);
    mapWorker.wsCreate();
    this.mapWorker = mapWorker;
  }

  _bindRenderEvent(mapRender) {
    const map2dStore = useMap2dStore();
    const map2dTopPanelStore = useMap2dTopPanelStore();
    mapRender.on("ready", () => {
      map2dStore.setMapReady(true);
      this.mapWorker.wsMapUpdate();
      if (__RMS_IS_DEV__) this.__openPixiDevTools();
    });
    mapRender.on("updated", data => {
      map2dTopPanelStore.setAbnormalRackCodes(data?.abnormalRackCodes);
      this.mapWorker && this.mapWorker.wsMapUpdate();
    });
    mapRender.on("click", data => {
      console.log("点击的data========", data);
      // if (data) {
      //   data.layer = "pallet";
      // }
      map2dStore.setMapClickData(data);
    });
    mapRender.on("rendered", data => map2dStore.setElementsPosition(data));
  }

  _bindWorkerEvent(mapWorker) {
    const map2dStore = useMap2dStore();
    const map2dAreaStore = useMap2dAreaStore();
    const map2dAbnormalStore = useMap2dAbnormalStore();
    const map2dInfoStore = useMap2dInfoStore();
    const map2dTopPanelStore = useMap2dTopPanelStore();
    const mapRender = this.mapRender;

    mapWorker.wsOn("wsInit", this.wsInit.bind(this));
    // 监听地图配置信息
    mapWorker.wsOn("wsMapConfig", config => {
      map2dStore.setMapConfig(config);
      if (config?.released) map2dInfoStore.setMapReleased(config?.released);
    });
    mapWorker.wsOn("wsUpdate", (display, mapAreas) => {
      map2dInfoStore.calFps();
      map2dAreaStore.updateMapAreas(mapAreas);
      mapRender.updateMap(display);
      map2dTopPanelStore.setAbnormalBoxCodes(display.confirmBoxes); // 异常货箱
    });
    mapWorker.wsOn("wsEventMessage", data => map2dAbnormalStore.setEventMessageInfo(data.body)); // 左上面板异常信息
    // 消息待办数量
    mapWorker.wsOn("wsMessageCount", data => map2dAbnormalStore.setMessageCountInfo(data.body));
    // 机器人统计信息
    mapWorker.wsOn("wsRobotStat", data => map2dAbnormalStore.setRobotStat(data.body));
    // 任务统计信息
    mapWorker.wsOn("wsTaskStat", data => map2dAbnormalStore.setTaskStat(data.body));
    // 设备统计信息
    mapWorker.wsOn("wsDeviceStat", data => map2dAbnormalStore.setDeviceStat(data.body));
    // 死锁信息
    mapWorker.wsOn("wsDeadLockRobots", data => {
      map2dStore.setDeadLockRobots(data);
      this.mapRender.renderBiz("deadLockRobot", data, false);
    });

    mapWorker.wsOn("wsError", this.wsError.bind(this));
  }

  wsInit(data) {
    if (!this.mapRender) return;
    const map2dStore = useMap2dStore();
    const map2dInfoStore = useMap2dInfoStore();
    const map2dAreaStore = useMap2dAreaStore();
    const map2dFastSearchStore = useMap2dFastSearchStore();
    const map2dAbnormalStore = useMap2dAbnormalStore();

    const dataType = data.type;
    switch (dataType) {
      case "MapFloor":
        map2dAreaStore.resetStore(); // 重置区域数据
        map2dStore.setMapClickData(null);
        const mapFloor = data?.mapFloor || {};
        this.mapRender.renderMap(mapFloor.floors, mapFloor.floorIds);
        // 这里去调用UI相关事件，相关数据有 mapId、mapName、updateTime、release、floorStatus
        map2dInfoStore.setMapInfo({
          mapId: mapFloor.mapId,
          mapName: mapFloor.mapName,
          released: mapFloor.released,
          updateTime: mapFloor.updateTime,
          systemStartupTime: mapFloor.systemStartupTime,
          mapVersion: mapFloor.mapVersion,
          floorStatus: mapFloor.floorStatus,
        });

        // 请求顶部代办和消息面板的一些状态
        map2dAbnormalStore.requestMessageTodoCount(); // 开始请求顶部代办信息
        map2dAbnormalStore.requestAllStat(); // 开始请求机器人 设备 任务 数量
        return;
      case "MapAreas":
        map2dStore.setWsLoading(false);
        map2dAreaStore.setMapAreas(data.mapAreas);
        map2dFastSearchStore.sendFastSearchRequestMsg();
        return;
    }
  }

  wsError(msg) {
    const { error } = console;
    error(msg);
  }

  static destroy() {
    const instance = this._instance;
    if (instance?.mapRender) {
      instance.mapRender.destroy();
      instance.mapRender = undefined;
    }
    if (instance?.mapWorker) {
      instance.mapWorker.destroy();
      instance.mapWorker = undefined;
    }
    MapSingleton._instance = null;
  }

  _getMapAngle() {
    const config = useRootStore().rmsConfig || {};
    let mapAngle = config?.mapRotateAngle || 0;
    if (mapAngle <= -180) mapAngle = mapAngle + 360;
    else if (mapAngle > 180) mapAngle = mapAngle - 360;

    return mapAngle;
  }

  __openPixiDevTools() {
    const isOpenDevTools = import.meta.env.VITE_PIXI_DEV_TOOLS;
    if (isOpenDevTools == "true") {
      console.warn("您开启了 Pixi DevTools");
      const pixi_app = this.mapRender?.mapCore?.getMapApp();
      if (pixi_app) globalThis.__PIXI_APP__ = pixi_app;
    }
  }
}

function createMap2D($dom) {
  MapSingleton.createInstance($dom);
}

function getMap2D() {
  return MapSingleton.getInstance();
}

function destroyMap2D() {
  return MapSingleton.destroy();
}

export { createMap2D, getMap2D, destroyMap2D };
